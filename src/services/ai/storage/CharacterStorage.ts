/**
 * ========================================================================
 * CHARACTER STORAGE
 * ========================================================================
 *
 * Maneja el almacenamiento y gestión de personajes generados
 * Incluye persistencia, historial y validación
 * ========================================================================
 */

export interface GeneratedCharacter {
  id: string;
  name: string;
  generatedAt: Date;
  sessionId?: string;
  used: boolean;
  category?: 'real' | 'fictional' | 'historical' | 'unknown';
  source?: string; // De dónde se generó (ai, manual, etc.)
}

export interface CharacterHistory {
  characters: GeneratedCharacter[];
  lastGenerated: Date | null;
  totalGenerated: number;
  currentCharacter: GeneratedCharacter | null;
}

export class CharacterStorage {
  private serviceName = 'characterStorage';
  private storageKey = 'enygma_generated_character';
  private historyKey = 'enygma_character_history';

  // ========== GESTIÓN DEL PERSONAJE ACTUAL ==========

  /**
   * Guarda el personaje actual en localStorage
   */
  public saveCurrentCharacter(character: string, sessionId?: string): GeneratedCharacter {
    try {
      const characterData: GeneratedCharacter = {
        id: this.generateCharacterId(),
        name: character.trim(),
        generatedAt: new Date(),
        sessionId,
        used: false,
        category: this.categorizeCharacter(character),
        source: 'ai'
      };

      // Guardar en la clave simple para compatibilidad
      localStorage.setItem(this.storageKey, character);

      // Guardar en el historial
      this.addToHistory(characterData);

      return characterData;

    } catch (error) {
      console.error(`❌ [${this.serviceName}] Error guardando personaje:`, error);
      throw error;
    }
  }

  /**
   * Obtiene el personaje actual desde localStorage
   */
  public getCurrentCharacter(): string | null {
    try {
      return localStorage.getItem(this.storageKey);
    } catch (error) {
      console.warn(`⚠️ [${this.serviceName}] Error leyendo personaje actual:`, error);
      return null;
    }
  }

  /**
   * Elimina el personaje actual
   */
  public clearCurrentCharacter(): void {
    try {
      localStorage.removeItem(this.storageKey);

      // Marcar el último personaje como usado en el historial
      const history = this.getHistory();
      if (history.currentCharacter) {
        this.markCharacterAsUsed(history.currentCharacter.id);
      }
    } catch (error) {
      console.error(`❌ [${this.serviceName}] Error eliminando personaje:`, error);
    }
  }

  /**
   * Verifica si hay un personaje actual guardado
   */
  public hasCurrentCharacter(): boolean {
    return this.getCurrentCharacter() !== null;
  }

  // ========== GESTIÓN DEL HISTORIAL ==========

  /**
   * Obtiene el historial completo de personajes
   */
  public getHistory(): CharacterHistory {
    try {
      const stored = localStorage.getItem(this.historyKey);
      if (!stored) {
        return this.createEmptyHistory();
      }

      const parsed = JSON.parse(stored);

      // Convertir fechas de string a Date
      parsed.characters = parsed.characters.map((char: any) => ({
        ...char,
        generatedAt: new Date(char.generatedAt)
      }));

      if (parsed.lastGenerated) {
        parsed.lastGenerated = new Date(parsed.lastGenerated);
      }

      return parsed;
    } catch (error) {
      console.warn(`⚠️ [${this.serviceName}] Error cargando historial:`, error);
      return this.createEmptyHistory();
    }
  }

  /**
   * Guarda el historial en localStorage
   */
  private saveHistory(history: CharacterHistory): void {
    try {
      localStorage.setItem(this.historyKey, JSON.stringify(history));
    } catch (error) {
      console.error(`❌ [${this.serviceName}] Error guardando historial:`, error);
    }
  }

  /**
   * Añade un personaje al historial
   */
  private addToHistory(character: GeneratedCharacter): void {
    const history = this.getHistory();

    // Verificar si ya existe (evitar duplicados)
    const existingIndex = history.characters.findIndex(char =>
      char.name.toLowerCase() === character.name.toLowerCase()
    );

    if (existingIndex >= 0) {
      // Actualizar existente
      history.characters[existingIndex] = character;
    } else {
      // Añadir nuevo
      history.characters.unshift(character); // Añadir al principio
      history.totalGenerated++;
    }

    history.lastGenerated = character.generatedAt;
    history.currentCharacter = character;

    // Limitar el historial a los últimos 50 personajes
    if (history.characters.length > 50) {
      history.characters = history.characters.slice(0, 50);
    }

    this.saveHistory(history);
  }

  /**
   * Marca un personaje como usado
   */
  public markCharacterAsUsed(characterId: string): boolean {
    const history = this.getHistory();
    const character = history.characters.find(char => char.id === characterId);

    if (character) {
      character.used = true;
      this.saveHistory(history);
      return true;
    }

    console.warn(`⚠️ [${this.serviceName}] Personaje no encontrado para marcar como usado: ${characterId}`);
    return false;
  }

  /**
   * Obtiene personajes recientes
   */
  public getRecentCharacters(limit: number = 10): GeneratedCharacter[] {
    const history = this.getHistory();
    return history.characters
      .sort((a, b) => b.generatedAt.getTime() - a.generatedAt.getTime())
      .slice(0, limit);
  }

  /**
   * Obtiene personajes no usados
   */
  public getUnusedCharacters(): GeneratedCharacter[] {
    const history = this.getHistory();
    return history.characters.filter(char => !char.used);
  }

  /**
   * Busca personajes por nombre
   */
  public searchCharacters(searchText: string): GeneratedCharacter[] {
    const history = this.getHistory();
    const searchLower = searchText.toLowerCase();

    return history.characters.filter(char =>
      char.name.toLowerCase().includes(searchLower)
    );
  }

  /**
   * Obtiene personajes por categoría
   */
  public getCharactersByCategory(category: GeneratedCharacter['category']): GeneratedCharacter[] {
    const history = this.getHistory();
    return history.characters.filter(char => char.category === category);
  }

  // ========== ESTADÍSTICAS Y ANÁLISIS ==========

  /**
   * Obtiene estadísticas del historial
   */
  public getStats() {
    const history = this.getHistory();
    const categories = history.characters.reduce((acc, char) => {
      const cat = char.category || 'unknown';
      acc[cat] = (acc[cat] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      total: history.totalGenerated,
      stored: history.characters.length,
      used: history.characters.filter(char => char.used).length,
      unused: history.characters.filter(char => !char.used).length,
      categories,
      lastGenerated: history.lastGenerated,
      oldestStored: history.characters.length > 0
        ? history.characters.reduce((oldest, char) =>
            char.generatedAt < oldest ? char.generatedAt : oldest,
            history.characters[0].generatedAt
          )
        : null
    };
  }

  /**
   * Obtiene el personaje más usado
   */
  public getMostPopularCharacters(limit: number = 5): GeneratedCharacter[] {
    // Esta funcionalidad requeriría un contador de uso por personaje
    // Por ahora, devolvemos los más recientes usados
    const history = this.getHistory();
    return history.characters
      .filter(char => char.used)
      .sort((a, b) => b.generatedAt.getTime() - a.generatedAt.getTime())
      .slice(0, limit);
  }

  // ========== GESTIÓN Y MANTENIMIENTO ==========

  /**
   * Limpia todo el historial
   */
  public clearHistory(): void {
    try {
      localStorage.removeItem(this.historyKey);
    } catch (error) {
      console.error(`❌ [${this.serviceName}] Error limpiando historial:`, error);
    }
  }

  /**
   * Elimina personajes antiguos (más de X días)
   */
  public cleanOldCharacters(daysOld: number = 30): number {
    const history = this.getHistory();
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysOld);

    const initialCount = history.characters.length;
    history.characters = history.characters.filter(char =>
      char.generatedAt >= cutoffDate
    );

    const removedCount = initialCount - history.characters.length;

    if (removedCount > 0) {
      this.saveHistory(history);
    }

    return removedCount;
  }

  /**
   * Exporta el historial como JSON
   */
  public exportHistory(): string {
    const history = this.getHistory();
    return JSON.stringify(history, null, 2);
  }

  /**
   * Importa historial desde JSON
   */
  public importHistory(jsonString: string): boolean {
    try {
      const imported = JSON.parse(jsonString) as CharacterHistory;

      // Validar estructura
      if (!imported.characters || !Array.isArray(imported.characters)) {
        throw new Error('Formato JSON inválido');
      }

      // Convertir fechas
      imported.characters = imported.characters.map(char => ({
        ...char,
        generatedAt: new Date(char.generatedAt)
      }));

      if (imported.lastGenerated) {
        imported.lastGenerated = new Date(imported.lastGenerated);
      }

      this.saveHistory(imported);
      return true;
    } catch (error) {
      console.error(`❌ [${this.serviceName}] Error importando historial:`, error);
      return false;
    }
  }

  // ========== MÉTODOS PRIVADOS ==========

  private createEmptyHistory(): CharacterHistory {
    return {
      characters: [],
      lastGenerated: null,
      totalGenerated: 0,
      currentCharacter: null
    };
  }

  private generateCharacterId(): string {
    return `char-${Date.now()}-${Math.random().toString(36).substring(2, 8)}`;
  }

  private categorizeCharacter(character: string): GeneratedCharacter['category'] {
    const name = character.toLowerCase();

    // Patrones simples para categorizar
    const fictionalKeywords = ['pokemon', 'dragon ball', 'naruto', 'mario', 'sonic', 'link'];
    const historicalKeywords = ['cleopatra', 'napoleon', 'cesar', 'alejandro', 'leonardo da vinci'];

    if (fictionalKeywords.some(keyword => name.includes(keyword))) {
      return 'fictional';
    }

    if (historicalKeywords.some(keyword => name.includes(keyword))) {
      return 'historical';
    }

    // Por defecto, asumir que es real (persona famosa contemporánea)
    return 'real';
  }

  /**
   * Valida si un nombre de personaje es válido
   */
  public isValidCharacterName(name: string): boolean {
    if (!name || typeof name !== 'string') return false;

    const trimmed = name.trim();
    return trimmed.length >= 2 && trimmed.length <= 100;
  }

  /**
   * Normaliza el nombre del personaje
   */
  public normalizeCharacterName(name: string): string {
    return name
      .trim()
      .replace(/\s+/g, ' ') // Espacios múltiples a uno solo
      .replace(/[^\w\s\-'\.]/g, '') // Remover caracteres especiales excepto algunos
      .substring(0, 100); // Limitar longitud
  }
}

// ========== SINGLETON EXPORT ==========
export const characterStorage = new CharacterStorage();
